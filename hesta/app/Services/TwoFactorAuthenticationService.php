<?php

namespace App\Services;

use App\Exceptions\BusinessLogicException;
use Illuminate\Support\Facades\Mail;

class TwoFactorAuthenticationService
{
  private $sessionKeyLoginId;
  private $sessionKeyCode;
  private $sessionKeyTimeLimit;
  private $guard;

  /**
   * コンストラクタ
   */
  public function __construct($guard = 'admin')
  {
    $this->guard = auth()->guard($guard);

    $this->sessionKeyLoginId = 'guard.id.' . $guard;
    $this->sessionKeyCode = 'guard.code.' . $guard;
    $this->sessionKeyTimeLimit = 'guard.time_limit.' . $guard;
  }

  /**
   * セッション保持 確認.
   *
   * @return bool
   */
  public function hasSession()
  {
    $request = request();

    return true;
    // return $request->session()->has($this->sessionKeyTimeLimit) && $request->session()->has($this->sessionKeyCode) && $request->session()->has($this->sessionKeyLoginId);
  }

  /**
   * 認証コードを送信
   */
  public function sendMail($email = null)
  {
    $currentRequest = request();
    if (!empty($email)) {
      $code = $this->getCode();

      // セッション保存
      $currentRequest->session()->put([
        $this->sessionKeyCode => $code,
        $this->sessionKeyTimeLimit => now()->addMinutes(config('auth.two_factor_authentication_minutes')),
      ]);

      // メール送信
      Mail::send([], [], function ($message) use ($email, $code) {
        $body = "様\nこの度は、ご利用ありがとうございます。\nパスワード再設定は完了しました。\n"
          . "--------------------------------------------------\n"
          . 'code：' . $code;

        $message->to($email)
          ->subject('【HESTA LIFE】パスワード再設定完了のお知らせ')
          ->setBody($body);
      });
    } else {
      throw new BusinessLogicException('不正なアクセスです。');
    }
  }

  /**
   * コードの再送信
   */
  public function resendingMail()
  {
    // セッション確認
    if (!$this->hasSession()) {
      throw new BusinessLogicException('不正なアクセスです。ログインからやり直してください。');
    }

    $loginId = request()->session()->get($this->sessionKeyLoginId);
    $this->sendMail($this->guard->getProvider()->retrieveById($loginId)->toArray());
  }

  /**
   * ログインを試みる.
   */
  public function attempt($request)
  {
    // セッション確認
    if (!$this->hasSession()) {
      throw new BusinessLogicException('不正なアクセスです。ログインからやり直してください。');
    }

    // 認証コードの使用期限を超えている場合
    if ($request->session()->get($this->sessionKeyTimeLimit) <= now()) {
      throw new BusinessLogicException('認証コードの使用期限切れです。再度コードを発行してください。');
    }

    // 認証コードが違う場合
    if ($request->session()->get($this->sessionKeyCode) !== $request->input('two_factor_authentication_code')) {
      throw new BusinessLogicException('認証コードが違います。');
    }

    return $this->guard->loginUsingId(
      $request->session()->get($this->sessionKeyLoginId),
      $request->input('remember') ? true : false
    );
  }

  /**
   * 6桁数字 コード発行.
   * @return int
   */
  private function getCode()
  {
    return str_pad(rand(0, 999999), 6, 0, STR_PAD_LEFT);
  }
}
