<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\TwoFactorAuthenticationService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as ControllerHybrid;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Session;

class Guard extends ControllerHybrid
{
  use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

  /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

  // use AuthenticatesUsers;

  /**
   * Where to redirect users after login.
   *
   * @var string
   */
  protected $redirectTo = '/guard/login';

  protected TwoFactorAuthenticationService $twoFactorAuthenticationService;

  /**
   * Create a new controller instance.
   */
  public function __construct()
  {
    $this->twoFactorAuthenticationService = new TwoFactorAuthenticationService('admin');
  }

  public function getIndex(Request $request)
  {
    $this->actionLogin($request);
  }

  public function actionLogin(Request $request)
  {
    if (session('auth.access_token')) {
      return redirect($request->query('ref', 'admin/standard/home'));
    }
    // ログインフォームが投稿された場合
    if ($request->isMethod('post')) {
      $response = Http::post(env('PLATFORM_CORE_URL') . '/auth/login', [
        'type' => 'mail',
        'username' => $request->input('email'),
        'password' => $request->input('password'),
      ])->json();

      if ($response['status_code'] == 200 && $response['data']['user_role']) {
        // 2段階認証をおこなう
        $this->twoFactorAuthenticationService->sendMail($request->input('email'));
      //   session(['auth' => $response['data']]);

        // return redirect($request->query('ref', 'admin/guard/two_factor_authentication'));
        return redirect()
          ->route(
            'admin.guard.two_factor_authentication',
            ['ref' => $request->query('ref')]
          );
      }

      // ログイン可能か確認
      $request->authenticate('admin');

      $request->session()->regenerate();

      return redirect($request->query('ref', 'admin/standard/home'));
    }

    return view('admin.guard.login', ['title' => 'ログイン']);
  }

  /**
   * 2段階認証 入力画面.
   */
  public function getTwoFactorAuthentication()
  {
    // dd($this->twoFactorAuthenticationService->hasSession());
    if ($this->twoFactorAuthenticationService->hasSession()) {
      return view('admin.guard.two_factor_authentication', ['title' => '２段階認証']);
    }

    return redirect()->route('admin.guard.login')->withInput()->with('error', '不正なアクセスです。ログインからやり直してください。');
  }

  /**
   * 2段階認証 認証
   */
  public function postTwoFactorAuthentication(Request $request)
  {
    $validator = Validator::make($request->post(), [
      'two_factor_authentication_code' => ['numeric', 'required'],
    ]);

    // バリデーション
    if ($validator->fails()) {
      return $this->responseError(400);
    }

    // 認証をおこない成功した場合ログインしてホーム画面へ
    if ($this->twoFactorAuthenticationService->attempt($request)) {
      return redirect($request->query('ref', 'admin/contract/order/list'));
    }

    return back()->withInput()->with('error', '不正なアクセスです。ログインからやり直してください。');
  }

  /**
   * Show the application logout.
   *
   * @return \Illuminate\Http\Response
   */
  public function getLogout()
  {
    $response = Http::withToken(session('auth.access_token'))->get(env('PLATFORM_CORE_URL') . '/auth/logout', [])->json();
    auth()->guard('admin')->logout();
    Session::flush();

    return redirect('admin/guard/login')->send();
  }

  public function actionForgetPassword(Request $request)
  {
    if (session('auth.access_token')) {
      return redirect($request->query('ref', 'admin/standard/home'));
    }

    if ($request->isMethod('post')) {
      $response = Http::post(env('PLATFORM_CORE_URL') . '/auth/forgot_password', [
        'email' => $request->input('email'),
      ])->json();

      if ($response['status_code'] == 200) {
        return redirect()->back()->with('success', 'パスワード再設定を受け付けました。');
      } else {
        return redirect()->back()->with('fail', '一致するユーザは見つかりませんでした。');
      }
    }

    return view('admin.guard.forget_password', ['title' => 'パスワードのリセット']);
  }

  public function actionResetPassword(Request $request)
  {
    if ($request->isMethod('post')) {
      $response = Http::post(env('PLATFORM_CORE_URL') . '/auth/reset_password', [
        'new_password' => $request->input('new_password'),
        'verify_token' => $request->input('token'),
      ])->json();

      if ($response['status_code'] == 200) {
        return redirect()->back()->with('success', 'パスワード再設定は完了しました。');
      } else {
        return redirect()->back()->with('fail', ' 完了できません');
      }
    }

    return view('admin.guard.reset_password', ['title' => 'パスワードを再設定する']);
  }
}
